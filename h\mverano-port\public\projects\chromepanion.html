<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chromepanion - Browser Extension</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .project-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .features {
            list-style: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .features li:before {
            content: "🚀 ";
            font-weight: bold;
        }
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chromepanion</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Your intelligent browsing companion that enhances productivity and provides smart assistance while you browse.
        </p>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">TypeScript</span>
                    <span class="tech-tag">Chrome Extension API</span>
                    <span class="tech-tag">React</span>
                    <span class="tech-tag">Webpack</span>
                    <span class="tech-tag">AI/ML APIs</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Beta Testing</p>
                <p><strong>Version:</strong> 2.1.0</p>
                <p><strong>Users:</strong> 1,200+ beta testers</p>
                <p><strong>Rating:</strong> 4.9/5 stars</p>
            </div>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>AI-powered content summarization and analysis</li>
                <li>Smart bookmark organization with auto-tagging</li>
                <li>Real-time language translation overlay</li>
                <li>Password security analysis and suggestions</li>
                <li>Tab management with intelligent grouping</li>
                <li>Privacy protection and tracker blocking</li>
                <li>Custom shortcuts and automation workflows</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                Chromepanion is an advanced browser extension that acts as your personal browsing assistant. 
                Using cutting-edge AI technology, it provides intelligent insights, automates repetitive tasks, 
                and enhances your overall browsing experience.
            </p>
            <p>
                The extension learns from your browsing patterns to offer personalized suggestions, helps you 
                stay organized with smart bookmark management, and protects your privacy while you explore the web. 
                Perfect for power users who want to maximize their productivity online.
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
