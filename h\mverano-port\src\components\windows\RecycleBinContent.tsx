
import { useState } from 'react';
import { recycleBinFiles } from './recycle-bin/recycleBinData';
import RecycleBinFileList from './recycle-bin/RecycleBinFileList';
import RecycleBinFileViewer from './recycle-bin/RecycleBinFileViewer';
import RecycleBinEmptyState from './recycle-bin/RecycleBinEmptyState';

const RecycleBinContent = () => {
  const [selectedFile, setSelectedFile] = useState<string | null>(null);

  const selectedFileData = recycleBinFiles.find(f => f.id === selectedFile);

  return (
    <div className="h-full flex bg-gray-50 font-tahoma">
      <RecycleBinFileList 
        files={recycleBinFiles}
        selectedFile={selectedFile}
        onFileSelect={setSelectedFile}
      />

      <div className="flex-1 flex flex-col">
        {selectedFile && selectedFileData ? (
          <RecycleBinFileViewer file={selectedFileData} />
        ) : (
          <RecycleBinEmptyState />
        )}
      </div>
    </div>
  );
};

export default RecycleBinContent;
