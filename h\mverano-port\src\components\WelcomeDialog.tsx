import { FileText, Folder, Mail, X } from 'lucide-react';

interface WelcomeDialogProps {
  onClose: () => void;
  onOpenWindow: (iconId: string, iconName: string) => void;
}

const WelcomeDialog = ({ onClose, onOpenWindow }: WelcomeDialogProps) => {
  const handleAction = (id: string, name: string) => {
    onOpenWindow(id, name);
    onClose();
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-30 z-[9998] flex items-center justify-center animate-in fade-in"
      onClick={onClose}
    >
      <div 
        className="bg-[#ECE9D8] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl w-[450px] rounded-lg overflow-hidden animate-slide-up"
        onClick={(e) => e.stopPropagation()}
        style={{ fontFamily: 'Tahoma, sans-serif' }}
      >
        {/* Title Bar */}
        <div className="bg-gradient-to-r from-[#2155C4] to-[#4D90FE] text-white font-bold text-sm px-3 py-1 flex justify-between items-center cursor-default">
          <span>Welcome!</span>
          <button onClick={onClose} className="bg-[#E2614A] hover:bg-[#F07A68] border border-[#8C3B2E] text-white w-5 h-5 flex items-center justify-center rounded-sm text-xs font-mono">
            <X size={14} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-4 text-sm">
          <div className="flex items-start space-x-3">
            <img src="/favicon.ico" alt="Windows XP Logo" className="w-10 h-10 mt-1" />
            <div>
              <h2 className="font-bold text-lg mb-2">Welcome to my Portfolio!</h2>
              <p className="mb-4">
                This is an interactive portfolio inspired by Windows XP.
                You can explore by double-clicking desktop icons or using the Start menu.
              </p>
              <p className="font-semibold mb-2">For quick access:</p>
            </div>
          </div>
          
          <div className="mt-2 space-y-2">
            <button 
              onClick={() => handleAction('my_resume', 'my_resume.pdf')}
              className="w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left"
            >
              <FileText className="text-red-600" size={20} />
              <span className="font-semibold">View Resume</span>
            </button>
            <button 
              onClick={() => handleAction('projects', 'My Projects')}
              className="w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left"
            >
              <Folder className="text-yellow-600" size={20} />
              <span className="font-semibold">See My Projects</span>
            </button>
            <button 
              onClick={() => handleAction('contact', 'Contact Info')}
              className="w-full flex items-center space-x-3 px-3 py-2 bg-white border rounded hover:bg-gray-100 transition-colors text-left"
            >
              <Mail className="text-red-600" size={20} />
              <span className="font-semibold">Get In Touch</span>
            </button>
          </div>

          <div className="text-right mt-4">
            <button 
              onClick={onClose}
              className="px-4 py-1 bg-gray-200 border border-gray-400 rounded-sm hover:bg-gray-300"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeDialog;
