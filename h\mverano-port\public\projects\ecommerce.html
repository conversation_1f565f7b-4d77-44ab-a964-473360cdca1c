<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .project-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .features {
            list-style: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .features li:before {
            content: "🛒 ";
            font-weight: bold;
        }
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ E-Commerce Platform</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            A comprehensive full-stack e-commerce solution with modern features and scalable architecture.
        </p>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">React</span>
                    <span class="tech-tag">Node.js</span>
                    <span class="tech-tag">PostgreSQL</span>
                    <span class="tech-tag">Express.js</span>
                    <span class="tech-tag">Redis</span>
                    <span class="tech-tag">AWS</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Production</p>
                <p><strong>Version:</strong> 4.1.2</p>
                <p><strong>Users:</strong> 10K+ registered users</p>
                <p><strong>Performance:</strong> 99.9% uptime</p>
            </div>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>User authentication and authorization system</li>
                <li>Secure payment processing with multiple gateways</li>
                <li>Comprehensive admin dashboard and analytics</li>
                <li>Product catalog with advanced search and filtering</li>
                <li>Shopping cart and wishlist functionality</li>
                <li>Order tracking and management system</li>
                <li>Inventory management and stock alerts</li>
                <li>Customer reviews and rating system</li>
                <li>Email notifications and marketing automation</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                A robust, scalable e-commerce platform built with modern web technologies. This full-stack 
                application provides a complete online shopping experience with features for both customers 
                and administrators.
            </p>
            <p>
                The platform includes secure user authentication, payment processing, inventory management, 
                and a powerful admin dashboard for managing products, orders, and customers. Built with 
                performance and security in mind, it can handle high traffic loads and provides a seamless 
                shopping experience across all devices.
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
