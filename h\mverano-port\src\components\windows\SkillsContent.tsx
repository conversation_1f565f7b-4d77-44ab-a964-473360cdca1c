
import { iconMap } from '../../lib/icon-map';

const SkillsContent = () => {
  const skillCategories = [
    {
      title: 'Programming Languages & Frameworks',
      skills: ['C#', 'VB.NET', 'ASP.NET', 'Razor', 'Node.js', '.NET Framework & Core', 'JavaScript', 'TypeScript', 'HTML', 'CSS']
    },
    {
      title: 'Frontend Technologies',
      skills: ['React', 'Angular', 'jQuery']
    },
    {
      title: 'Databases & Data Access',
      skills: ['MSSQL', 'MySQL', 'PostgreSQL', 'SQLite', 'CosmosDB', 'NoSQL (ElasticSearch)', 'T-SQL', 'LINQ', 'ADO.NET', 'Dapper']
    },
    {
      title: 'Development Tools',
      skills: ['Visual Studio', 'VS Code', 'DevExpress', 'Postman', 'Docker']
    },
    {
      title: 'Data & Analytics',
      skills: ['Metabase', 'JSON', 'XML', 'Web API']
    },
    {
      title: 'Version Control',
      skills: ['GitHub', 'GitLab', 'TFS', 'SVN', 'Bitbucket']
    },
    {
      title: 'Project Management',
      skills: ['JIRA', 'Trello', 'Asana', 'Notion', 'Confluence']
    }
  ];

  return (
    <div className="p-4 bg-white font-tahoma">
      <div className="flex items-center mb-4 pb-2 border-b-2 border-blue-500">
        <span className="text-2xl mr-2">⚡</span>
        <h2 className="text-lg font-bold text-blue-800">Skills & Experience</h2>
      </div>
      
      <div className="bg-blue-50 p-3 rounded mb-4 border-l-4 border-blue-500">
        <p className="text-sm text-blue-800">
          <strong>Professional Summary:</strong> Experienced software developer with expertise in .NET technologies, 
          full-stack web development, and database management. Proficient in modern frameworks and development tools.
        </p>
      </div>

      {skillCategories.map(category => (
        <div key={category.title} className="mb-6">
          <h3 className="font-bold text-blue-800 mb-3 pb-1 border-b border-gray-300 bg-gradient-to-r from-blue-100 to-transparent px-2 py-1">
            {category.title}
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {category.skills.map(skill => {
              const skillData = iconMap[skill];
              const IconComponent = skillData?.icon;
              
              return (
                <div key={skill} className="flex items-center space-x-2 p-2 bg-gray-50 rounded border hover:bg-blue-50 transition-colors">
                  {IconComponent ? (
                    <IconComponent className={`w-4 h-4 ${skillData.color}`} />
                  ) : (
                    <span className="text-lg">🔧</span>
                  )}
                  <span className="text-sm font-medium">{skill}</span>
                </div>
              );
            })}
          </div>
        </div>
      ))}

      <div className="mt-6 p-4 bg-gray-100 rounded border">
        <h3 className="font-bold text-gray-800 mb-2">Current Focus Areas</h3>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• Cloud-native application development</li>
          <li>• Microservices architecture with .NET</li>
          <li>• Modern frontend frameworks (React, Angular)</li>
          <li>• DevOps and containerization with Docker</li>
          <li>• Database optimization and performance tuning</li>
        </ul>
      </div>
    </div>
  );
};

export default SkillsContent;
