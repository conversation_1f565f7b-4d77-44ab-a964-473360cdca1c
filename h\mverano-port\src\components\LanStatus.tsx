
import React from 'react';
import { X } from 'lucide-react';

const Tachometer = () => {
    const level = 'Senior';
    const rotation = {
        'Junior': -75,
        'Mid': 0,
        'Senior': 75
    };
    const angle = rotation[level];

    return (
        <div className="relative w-48 h-24 flex items-center justify-center">
            {/* Half-circle background */}
            <div 
                className="absolute w-full h-full border-4 border-gray-400 rounded-t-full border-b-0 bg-[#F5F5F5]"
                style={{
                    boxSizing: 'border-box'
                }}
            />
            
            {/* Labels */}
            <div className="absolute bottom-1 left-2 text-[10px] font-bold text-gray-700 select-none">Junior</div>
            <div className="absolute top-1 left-1/2 -translate-x-1/2 text-[10px] font-bold text-gray-700 select-none">Mid</div>
            <div className="absolute bottom-1 right-2 text-[10px] font-bold text-gray-700 select-none">Senior</div>

            {/* Indicator needle */}
            <div 
                className="absolute bottom-0 left-1/2 h-[88px] w-0.5 origin-bottom transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-50%) rotate(${angle}deg)` }}
            >
                <div className="w-full h-full bg-red-600"/>
            </div>

           {/* Center pin */}
           <div className="absolute bottom-[-8px] left-1/2 -translate-x-1/2 w-4 h-4 bg-gray-600 rounded-full border-2 border-gray-400 z-10" />
        </div>
    );
};

const LanStatus = ({ onClose }: { onClose?: () => void }) => {
  return (
    <div
      className="w-[80vw] max-w-64 bg-[#ECE9D8] border border-gray-400 rounded-sm p-1 font-tahoma text-xs shadow-lg text-black"
      style={{
        fontFamily: 'Tahoma, sans-serif',
        boxShadow: '2px 2px 4px rgba(0,0,0,0.5), inset 1px 1px 0px #FFF, inset -1px -1px 0px #848278',
        borderTop: '1px solid #FFFFFF',
        borderLeft: '1px solid #FFFFFF',
        borderRightColor: '#A0A0A0',
        borderBottomColor: '#A0A0A0',
        background: '#DFDDCF'
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="flex items-center justify-between mb-2 px-1 py-0.5 bg-gradient-to-r from-[#0058E0] to-[#3891F8]">
        <span className="font-bold text-white select-none">Experience Level Status</span>
        {onClose && (
          <button
            onClick={onClose}
            className="bg-[#E2614A] hover:bg-[#F0715A] w-4 h-4 flex items-center justify-center rounded-sm"
            style={{
              borderTop: '1px solid #FFF',
              borderLeft: '1px solid #FFF',
              borderRight: '1px solid #000',
              borderBottom: '1px solid #000',
            }}
          >
            <X size={10} className="text-white" strokeWidth={2.5} />
          </button>
        )}
      </div>
      <div className="flex flex-col items-center justify-center px-3 pb-3 pt-1">
        <Tachometer />
        <p className="mt-4 text-center select-none">Connection Speed: Senior Level</p>
        <p className="text-center text-gray-600 text-[10px] select-none">Max throughput achieved.</p>
      </div>
    </div>
  );
};

export default LanStatus;
