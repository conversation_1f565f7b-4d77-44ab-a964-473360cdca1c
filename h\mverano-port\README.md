# Windows XP Portfolio

A nostalgic Windows XP-themed portfolio website built with modern web technologies.

## Getting Started

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### Installation

```sh
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to the project directory
cd <YOUR_PROJECT_NAME>

# Install dependencies
npm install

# Start the development server
npm run dev
```

## Technologies Used

This project is built with:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript
- **React** - UI library
- **shadcn/ui** - Modern UI components
- **Tailwind CSS** - Utility-first CSS framework

## Development

### Local Development

Run the development server:

```sh
npm run dev
```

The application will be available at `http://localhost:8080`

### Building for Production

```sh
npm run build
```

### Preview Production Build

```sh
npm run preview
```

## Deployment

This project can be deployed to any static hosting service such as:

- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- Any other static hosting provider

Simply build the project and upload the `dist` folder to your hosting service.
