
import { useState, useEffect, useCallback } from 'react';

export interface WindowData {
    position: { x: number; y: number };
    size: { width: number; height: number };
    isMaximized?: boolean;
}
  
export interface UseWindowResizeProps {
    windowData: WindowData;
    onUpdatePosition: (position: { x: number; y: number }) => void;
    onUpdateSize?: (size: { width: number; height: number }) => void;
    onBringToFront: () => void;
    isMobile?: boolean;
}

export const useWindowResize = ({ windowData, onUpdatePosition, onUpdateSize, onBringToFront, isMobile }: UseWindowResizeProps) => {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<string>('');
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });

  const { size, position, isMaximized } = windowData;

  const handleResizeStart = useCallback((e: React.MouseEvent, direction: string) => {
    if (isMaximized || isMobile || !onUpdateSize) return;
    
    e.stopPropagation();
    setIsResizing(true);
    setResizeDirection(direction);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    });
    onBringToFront();
  }, [isMaximized, isMobile, onUpdateSize, onBringToFront, size.width, size.height]);
  
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !onUpdateSize) return;

      const deltaX = e.clientX - resizeStart.x;
      const deltaY = e.clientY - resizeStart.y;
      
      let newWidth = resizeStart.width;
      let newHeight = resizeStart.height;
      let newX = position.x;
      let newY = position.y;

      const minWidth = 300;
      const minHeight = 200;

      if (resizeDirection.includes('right')) {
        newWidth = Math.max(minWidth, resizeStart.width + deltaX);
        newWidth = Math.min(newWidth, globalThis.window.innerWidth - position.x);
      }
      if (resizeDirection.includes('left')) {
        const proposedWidth = resizeStart.width - deltaX;
        if (proposedWidth >= minWidth) {
          newWidth = proposedWidth;
          newX = position.x + deltaX;
          if (newX < 0) {
            newWidth += newX;
            newX = 0;
          }
        }
      }
      if (resizeDirection.includes('bottom')) {
        newHeight = Math.max(minHeight, resizeStart.height + deltaY);
        newHeight = Math.min(newHeight, globalThis.window.innerHeight - position.y - 40);
      }
      if (resizeDirection.includes('top')) {
        const proposedHeight = resizeStart.height - deltaY;
        if (proposedHeight >= minHeight) {
          newHeight = proposedHeight;
          newY = position.y + deltaY;
          if (newY < 0) {
            newHeight += newY;
            newY = 0;
          }
        }
      }

      onUpdateSize({ width: newWidth, height: newHeight });
      if (newX !== position.x || newY !== position.y) {
        onUpdatePosition({ x: newX, y: newY });
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizeDirection('');
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, resizeDirection, resizeStart, position, onUpdateSize, onUpdatePosition]);

  return { handleResizeStart };
};
