
export interface PortfolioItem {
  id: string;
  name: string;
  icon: string;
}

export interface OpenWindow {
  id: string;
  title: string;
  icon: string;
  content: string | React.ReactNode;
  isMinimized: boolean;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
  isMaximized?: boolean;
  originalPosition?: { x: number; y: number };
  originalSize?: { width: number; height: number };
}
