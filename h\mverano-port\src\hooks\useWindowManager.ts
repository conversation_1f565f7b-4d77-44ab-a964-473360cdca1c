
import { useState } from 'react';
import { OpenWindow } from '../types';
import { desktopIcons, portfolioItems } from '../lib/constants';
import { getWindowContent } from '../lib/windowContent';

export const useWindowManager = (isMobile: boolean) => {
  const [openWindows, setOpenWindows] = useState<OpenWindow[]>([]);
  const [maxZIndex, setMaxZIndex] = useState(1000);

  const openWindow = (iconId: string, iconName: string) => {
    const allIcons = [...desktopIcons, ...portfolioItems];
    const iconData = allIcons.find(i => i.id === iconId);

    const existingWindow = openWindows.find(w => w.id === iconId);
    if (existingWindow) {
      setOpenWindows(prev => prev.map(w => 
        w.id === iconId 
          ? { ...w, isMinimized: false, zIndex: maxZIndex + 1 }
          : w
      ));
      setMaxZIndex(prev => prev + 1);
      return;
    }

    const newWindow: OpenWindow = {
      id: iconId,
      title: iconName,
      icon: iconData?.icon || '📄',
      content: getWindowContent(iconId, openWindow),
      isMinimized: false,
      position: isMobile
        ? { x: 0, y: 0 }
        : { x: 100 + openWindows.length * 30, y: 100 + openWindows.length * 30 },
      size: isMobile
        ? { width: globalThis.window.innerWidth, height: globalThis.window.innerHeight - 40 }
        : iconId === 'my_resume'
        ? { width: 1200, height: 800 }
        : iconId === 'my_computer'
        ? { width: 700, height: 500 }
        : iconId === 'recyclebin'
        ? { width: 650, height: 500 }
        : iconId === 'my_music'
        ? { width: 480, height: 360 }
        : iconId === 'my_pictures'
        ? { width: 750, height: 550 }
        : iconId.startsWith('project-')
        ? { width: 900, height: 700 }
        : { width: 600, height: 400 },
      zIndex: maxZIndex + 1,
      isMaximized: !!isMobile,
    };

    setOpenWindows(prev => [...prev, newWindow]);
    setMaxZIndex(prev => prev + 1);
  };

  const closeWindow = (windowId: string) => {
    setOpenWindows(prev => prev.filter(w => w.id !== windowId));
  };

  const minimizeWindow = (windowId: string) => {
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, isMinimized: true } : w
    ));
  };

  const maximizeWindow = (windowId: string) => {
    if (isMobile) return;
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId 
        ? { 
            ...w,
            originalPosition: w.isMaximized ? w.originalPosition : w.position,
            originalSize: w.isMaximized ? w.originalSize : w.size,
            position: { x: 0, y: 0 }, 
            size: { width: window.innerWidth, height: window.innerHeight - 40 },
            zIndex: maxZIndex + 1,
            isMaximized: true
          } 
        : w
    ));
    setMaxZIndex(prev => prev + 1);
  };

  const restoreWindow = (windowId: string) => {
    if (isMobile) return;
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId && w.originalPosition && w.originalSize
        ? { 
            ...w,
            position: w.originalPosition,
            size: w.originalSize,
            zIndex: maxZIndex + 1,
            isMaximized: false
          } 
        : w
    ));
    setMaxZIndex(prev => prev + 1);
  };

  const bringToFront = (windowId: string) => {
    const windowToFront = openWindows.find(w => w.id === windowId);
    if (windowToFront && windowToFront.zIndex === maxZIndex) {
      return;
    }

    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, zIndex: maxZIndex + 1 } : w
    ));
    setMaxZIndex(prev => prev + 1);
  };

  const updateWindowPosition = (windowId: string, position: { x: number; y: number }) => {
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, position } : w
    ));
  };

  const updateWindowSize = (windowId: string, size: { width: number; height: number }) => {
    setOpenWindows(prev => prev.map(w => 
      w.id === windowId ? { ...w, size } : w
    ));
  };

  const handleTaskbarClick = (windowId: string) => {
    const window = openWindows.find(w => w.id === windowId);
    if (window?.isMinimized) {
      setOpenWindows(prev => prev.map(w => 
        w.id === windowId 
          ? { ...w, isMinimized: false, zIndex: maxZIndex + 1 }
          : w
      ));
      setMaxZIndex(prev => prev + 1);
    } else {
      bringToFront(windowId);
    }
  };

  return {
    openWindows,
    openWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    bringToFront,
    updateWindowPosition,
    updateWindowSize,
    handleTaskbarClick,
  };
};
