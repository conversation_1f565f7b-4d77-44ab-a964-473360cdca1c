
export interface RecycleBinFile {
  id: string;
  name: string;
  type: string;
  size: string;
  modified: string;
  content: string;
}

export const recycleBinFiles: RecycleBinFile[] = [
  {
    id: 'bad_code',
    name: 'bad_code.js',
    type: 'JavaScript File',
    size: '2.3 KB',
    modified: '2019-03-15',
    content: `// Oh the humanity! My early JavaScript adventures...

// Exhibit A: Global variable soup
var name;
var age;
var email;
var address;
var phone;
var favoriteColor;
var petName;
// ... and 47 more global variables

// Exhibit B: The infamous pyramid of doom
function getUserData(userId) {
  $.ajax({
    url: '/api/user/' + userId,
    success: function(user) {
      $.ajax({
        url: '/api/user/' + userId + '/posts',
        success: function(posts) {
          $.ajax({
            url: '/api/user/' + userId + '/comments',
            success: function(comments) {
              // Finally! Only 3 callbacks deep...
              displayUserProfile(user, posts, comments);
            }
          });
        }
      });
    }
  });
}

// Exhibit C: The classic "works on my machine"
if (navigator.userAgent.indexOf("Chrome") > -1) {
  // Code that only works in Chrome on Windows 10
  // on a Tuesday, when <PERSON> is in retrograde
}`
  },
  {
    id: 'first_website',
    name: 'first_website.html',
    type: 'HTML Document',
    size: '15.7 KB',
    modified: '2018-07-22',
    content: `<!DOCTYPE html>
<html>
<head>
  <title>WELCOME TO MY AWESOME WEBSITE!!!</title>
  <style>
    /* I just discovered CSS animations... */
    body {
      background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff, #ffff00);
      animation: rainbow 2s infinite;
      font-family: Comic Sans MS; /* The pinnacle of web design */
    }

    @keyframes rainbow {
      0% { filter: hue-rotate(0deg); }
      100% { filter: hue-rotate(360deg); }
    }

    .blink {
      animation: blink 0.5s infinite;
    }

    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0; }
    }
  </style>
</head>
<body>
  <center> <!-- Because centering was hard back then -->
    <h1 class="blink">JOHN'S SUPER COOL WEBSITE </h1>
    <marquee>Welcome to the future of web design!</marquee>

    <table border="1" cellpadding="10" bgcolor="yellow">
      <tr>
        <td>
          <h2>About Me:</h2>
          <p>I like computers and stuff. I know HTML now so I'm basically a hacker.</p>

          <h2>My Skills:</h2>
          <ul>
            <li>HTML (I'm an expert now!)</li>
            <li>MS Paint</li>
            <li>Typing really fast</li>
          </ul>

          <h2>Guestbook:</h2>
          <p>Please sign my guestbook! (Coming soon...)</p>

          <h2>Hit Counter:</h2>
          <img src="counter.gif" alt="You are visitor #1">
          <!-- Spoiler: I was always visitor #1 -->
        </td>
      </tr>
    </table>

    <p><img src="under_construction.gif"> This site is under construction! <img src="under_construction.gif"></p>
  </center>
</body>
</html>

<!-- Current me looking at this: "How did anyone hire me?" -->`
  },
  {
    id: 'spaghetti_code',
    name: 'spaghetti_code.cs',
    type: 'C# File',
    size: '9.2 KB',
    modified: '2020-02-15',
    content: `// The legendary spaghetti monster I once called "clean code"
// This C# version is just as... flavorful.

using System;
using System.Linq;

namespace EarlyCareerMistakes
{
  public class SpaghettiCode
  {
    // Exhibit A: The pyramid of doom, C# edition
    public string ProcessUserData(object[] data)
    {
      if (data != null) {
        if (data.Length > 0) {
          if (data[0] != null) {
            if (data[0] is string strData && !string.IsNullOrWhiteSpace(strData)) {
              var userName = strData.Trim().ToLower();
              if (userName != "" && userName.Length >= 2 && userName.All(char.IsLetter)) {
                // I could have done this in one line with a null conditional and a regex.
                // Instead, I chose... this.
                return "Valid user!";
              }
            }
          }
        }
      }
      return "Invalid";
    }

    // Exhibit B: The "God Method" that does everything
    public string HandleEverything()
    {
      ConnectToDatabase();
      AuthenticateUser();
      ProcessSomeData();
      SendEmail();
      WriteToLog();
      SaveToFile();
      CallExternalApi();
      // ... and it's probably missing a try-catch block.
      return "Success... I think?";
    }

    // Exhibit C: My naming conventions were... creative
    public object Foo()
    {
      var bar = GetStuff();
      var thing = DoSomething(bar);
      // Why did I think this ternary was necessary?
      var result = thing != null ? thing : null;
      return result;
    }

    // Exhibit D: Copy-paste driven development
    public decimal CalculateTotal1(dynamic[] items)
    {
      decimal total = 0;
      foreach (var item in items) {
        total += item.Price * item.Quantity;
        if (item.Discount > 0) {
          total -= item.Discount;
        }
      }
      return total;
    }

    public decimal CalculateTotal2(dynamic[] items)
    {
      decimal total = 0;
      foreach (var item in items) {
        total += item.Price * item.Quantity;
        if (item.Discount > 0) {
          total -= item.Discount;
        }
      }
      return total;
    }
    // (Yes, they're identical. I probably got distracted by a squirrel.)

    // Modern me: *refactored into 15 clean, testable classes with dependency injection*
  }

  // Dummy implementations to make it look like it does something
  public partial class SpaghettiCode {
    private void CallExternalApi() {}
    private void SaveToFile() {}
    private void WriteToLog() {}
    private void SendEmail() {}
    private void ProcessSomeData() {}
    private object AuthenticateUser() => null;
    private object ConnectToDatabase() => null;
    private object GetStuff() => null;
    private object DoSomething(object bar) => null;
  }
}`
  },
  {
    id: 'debugging_tears',
    name: 'debugging_tears.txt',
    type: 'Text Document',
    size: '4.2 KB',
    modified: '2021-09-30',
    content: `THE GREAT DEBUGGING DISASTERS OF MY CAREER
==========================================

Bug #1: The Missing Semicolon Saga (3 days)
Date: March 2019
Problem: Page wouldn't load, 500 server error
Root cause: Missing semicolon in JavaScript
Debugging methods tried:
- Rewrote entire backend (twice)
- Questioned life choices
- Considered career change to farming
- Asked 17 different Stack Overflow questions
- Rubber duck debugging (the duck quit)
Resolution: Senior dev spotted it in 30 seconds

Bug #2: The Case-Sensitive Catastrophe (1 week)
Date: July 2020
Problem: Database queries failing in production but working locally
Debugging journey:
- Blamed the cloud provider
- Rewrote all SQL queries
- Created 47 test cases
- Migrated to different database engine
- Sacrificed coffee to the code gods
Root cause: Linux server was case-sensitive, Windows dev machine wasn't
MyTable ≠ mytable (who knew?) 

Bug #3: The Phantom Form Data (2 weeks)
Date: November 2020
Problem: Form submissions randomly disappearing
Suspects investigated:
- Network gremlins
- Database corruption
- Competitor sabotage
- Quantum interference
- User error (always blame the user first)
Real culprit: Browser autocomplete interfering with custom validation
The user WAS clicking submit... it just wasn't being submitted 

Bug #4: The Docker Dilemma (4 days)
Date: April 2021
Problem: "Works on my machine" but not in container
Emotional stages:
1. Denial: "Docker is broken"
2. Anger: "WHO WROTE THIS DOCKERFILE?!" (spoiler: it was me)
3. Bargaining: "Maybe if I restart everything 50 times..."
4. Depression: "I'll never understand containers"
5. Acceptance: "Oh, I forgot to expose port 3000" 

Lessons Learned:
- Sleep is not optional during debugging
- Coffee is a debugging tool, not a food group
- The bug is always in the last place you look (because you stop looking)
- "It can't be that simple" - it usually is
- Your rubber duck is your best friend
- Stack Overflow doesn't judge... much
- When in doubt, restart everything (including yourself)

Current debugging motto: "Every bug is just an opportunity to feel really 
stupid for a few hours before feeling really smart." `
  },
  {
    id: 'old_resume',
    name: 'old_resume_v1.doc',
    type: 'Word Document',
    size: '127 KB',
    modified: '2018-05-15',
    content: `JOHN SMITH - COMPUTER EXPERT
============================

OBJECTIVE:
To get a job with computers and make websites that are really cool.

SKILLS:
• Microsoft Word (Expert level - I can change fonts!)
• Internet Explorer (I know all the good websites)
• Copy and paste (I'm really fast at this)
• Typing (50 WPM on a good day)
• HTML (I made a webpage once)
• Computer troubleshooting (I know how to restart)

EXPERIENCE:
• Fixed my neighbor's computer by turning it off and on again (2018)
• Created a MySpace profile with custom music (2007)
• Won solitaire 3 times in a row (2017)
• Successfully installed a screensaver (2018)

EDUCATION:
• High School Diploma
• YouTube University (ongoing)
• Google Search Academy (self-taught)
• Stack Overflow Fellowship (unofficial)

PROJECTS:
• Built a website with flashing text and music that plays automatically
• Customized my Windows XP desktop with cool themes
• Learned to use Ctrl+C and Ctrl+V

REFERENCES:
• My mom (she says I'm really good with computers)
• My friend Steve (he taught me about right-clicking)

---

Current me looking at this: "Ah, the confidence of knowing just enough 
to be dangerous... and putting EVERYTHING in Comic Sans font." 

P.S. - Yes, I actually thought "Computer Expert" was a job title.
P.P.S. - The screensaver installation really was a proud moment.`
  },
  {
    id: 'imposter_syndrome',
    name: 'imposter_syndrome.exe',
    type: 'Application',
    size: '∞ KB',
    modified: 'Every day',
    content: ` IMPOSTER SYNDROME SIMULATOR 2024 
=====================================

[LOADING SELF-DOUBT...]
[████████████████████████████████] 100%

Daily Thoughts Include:
• "Everyone here is smarter than me"
• "I'm just googling everything"
• "They'll find out I don't know what I'm doing"
• "I probably got this job by accident"
• "Why did they ask ME to lead this project?"

Reality Check:
- You've successfully delivered 15+ projects
- Your code reviews are thorough and helpful
- Junior devs come to YOU for guidance
- You solved that critical production bug last month
- You've learned 8 new technologies this year

Plot Twist:
The fact that you worry about not knowing enough 
actually means you care about doing good work!

Remember:
• Senior developers Google things too
• Everyone started somewhere
• Your "obvious" solution helped 3 people today
• You're not supposed to know everything
• Growth means being uncomfortable sometimes

Achievement Unlocked:
"Turned imposter syndrome into motivation to keep learning"

[CONFIDENCE BOOST DELIVERED]
[CONTINUE BEING AWESOME? Y/N] → Y

---
"The only thing I know is that I know nothing... 
and that's actually pretty valuable in tech!" - Socrates (probably)`
  },
  {
    id: 'stack_overflow_dependency',
    name: 'stack_overflow_dependency.dll',
    type: 'Dynamic Link Library',
    size: '42.0 GB',
    modified: 'Constantly',
    content: `STACK OVERFLOW DEPENDENCY MANAGER v2024.1
=========================================

USAGE STATISTICS:
• Questions asked: 47
• Questions answered: 3 (and one was marked as duplicate)
• Times saved by existing answers: 2,847
• Hours spent reading similar problems: 847
• "Thank you" comments left: 156

REPUTATION BREAKDOWN:
• Upvotes received: 23
• Downvotes received: 7 (apparently "just use jQuery" isn't always the answer)
• Best answer: +15 points for explaining CSS flexbox
• Most embarrassing question: -3 points for asking about HTML closing tags

MOST SEARCHED PHRASES:
1. "How to center a div"
2. "JavaScript array methods"
3. "Git undo last commit"
4. "CSS not working"
5. "Why is my React component not rendering"
6. "Difference between let and var"
7. "How to exit vim" (we've all been there)

CLASSIC RESPONSES ENCOUNTERED:
• "Possible duplicate of..." (usually not)
• "What have you tried?" (everything!)
• "This question is too broad"
• "Welcome to Stack Overflow! Please read the FAQ"
• "Try this: [one line of code that solves everything]"

KARMA PHILOSOPHY:
• Always upvote helpful answers
• Leave comments explaining what worked
• Pay it forward by answering when you can
• Remember: everyone was a beginner once

DEPENDENCY WARNING:
This library is critical to developer productivity.
Removal may result in:
- Severe coding paralysis
- Increased rubber duck conversations
- Excessive reading of documentation
- Actually understanding how things work (scary!)

ACKNOWLEDGMENTS:
Thank you to the anonymous heroes who answered 
"How to do [basic thing]" with patience and detailed examples.
You're the real MVPs of software development.

[STATUS: HEALTHY DEPENDENCY]
[UPDATES: AVAILABLE DAILY]`
  },
  {
    id: 'coffee_addiction',
    name: 'coffee_addiction.jar',
    type: 'Java Archive',
    size: '3.14 MB',
    modified: 'Every morning',
    content: `COFFEE DEPENDENCY INJECTION FRAMEWORK
==========================================

CONSUMPTION METRICS:
• Daily intake: 4.7 cups average
• Peak performance hours: 9-11 AM (post-coffee #2)
• Crash time: 3 PM (solution: more coffee)
• Weekend consumption: Doubled (no meetings to dilute coding time)

BREW CONFIGURATIONS:
• Development Environment: Light roast, extra caffeine
• Production Deployment: Dark roast, triple shot
• Bug Fixing Mode: Whatever's strongest
• Code Review Sessions: Decaf (need to be diplomatic)

CODE CORRELATION:
Lines of code vs. Coffee cups consumed:
0 cups  → Stares at screen, questions life choices
1 cup   → Opens IDE, reads yesterday's code
2 cups  → Actual coding begins
3 cups  → Peak productivity achieved
4+ cups → Either genius-level code or complete gibberish

WITHDRAWAL SYMPTOMS:
• Syntax errors increase by 347%
• Variable naming becomes questionable (x, xx, xxx)
• Comments consist of only "//TODO: fix this mess"
• Meetings become physically painful
• Considers PowerShell as a viable career alternative

PERFORMANCE OPTIMIZATION:
• Cold brew for sustained release
• Espresso for immediate compilation
• French press for long debugging sessions
• Energy drinks for production hotfixes (emergency use only)

ACHIEVEMENTS UNLOCKED:
- "Coffee Connoisseur" - Tried 47 different roasts
- "Morning Ritual Master" - Perfect brew timing
- "Team Supplier" - Always brings extra for colleagues
- "Crisis Manager" - Functions at 3 AM with proper fuel

HEALTH WARNINGS:
• Side effects may include: productivity, alertness, happiness
• Do not operate heavy machinery (deploy to production) without adequate doses
• Consult your IDE before changing to decaf

FUTURE ROADMAP:
• Integrate IoT coffee maker with CI/CD pipeline
• Automatic brewing triggered by failed builds
• Slack bot for coffee status updates
• Machine learning to optimize brew strength vs. code quality

[STATUS: ACTIVE DEPENDENCY]
[NEXT REFILL: In 47 minutes]`
  },
  {
    id: 'work_life_balance',
    name: 'work_life_balance.404',
    type: 'File Not Found',
    size: '0 KB',
    modified: 'Never',
    content: `ERROR 404: WORK-LIFE BALANCE NOT FOUND
======================================

SEARCH RESULTS:
We couldn't find "work_life_balance" in the following locations:
• /home/<USER>/life/
• /usr/local/priorities/
• /var/log/activities/
• /etc/schedule/
• /tmp/weekend_plans/ (directory empty)

LAST SEEN:
• Before learning to code
• During lunch breaks (deprecated)
• Weekends (status: hijacked by side projects)
• Vacation days (used for learning new frameworks)

CURRENT STATUS:
• Work hours: 9 AM - 5 PM (and 8 PM - 11 PM) (and weekends)
• Side projects: 47 active repositories
• Tutorial queue: 23 videos, 156 articles
• Sleep schedule: "I'll fix it tomorrow"
• Social life: Exists primarily on Discord servers

SYMPTOMS OF MISSING FILE:
• Dreams in JavaScript
• Explains APIs to confused family members
• Weekend "relaxation" involves coding tutorials
• Phone wallpaper is a terminal screenshot
• Considers Stack Overflow a social network

RECOVERY ATTEMPTS:
- [X] Set boundaries (lasted 3 days)
- [X] No-code weekends (built 2 apps)
- [X] Meditation app (analyzed its UI/UX instead)
- [X] Take up hiking (brought laptop to research trail apps)
- [X] Join a book club (suggested technical books only)

TROUBLESHOOTING STEPS:
1. Try turning off notifications (but what if there's an important GitHub issue?)
2. Schedule non-tech activities (ended up automating the scheduling)
3. Find hobbies unrelated to coding (built an app to track hobbies)
4. Spend time with non-tech friends (converted them to junior developers)

PLOT TWIST:
Maybe work-life balance is about loving what you do 
so much that the boundaries naturally blur?

CURRENT APPROACH:
• Work on passion projects (technically still work?)
• Code with friends (technically social time?)
• Automate life tasks (more time for coding!)
• Teaching others (spreading the joy)

WARNING:
This file may never be fully restored. 
Symptoms include excessive happiness, 
constant learning, and the urge to optimize everything.

[RESTORATION: IN PROGRESS...]
[ESTIMATED TIME: When robots do all the coding]
[PRIORITY: Lower than the current sprint]

P.S. - At least I'm consistent!`
  }
];
