
import { useState, useEffect } from 'react';
import BootScreen from '../components/BootScreen';
import Desktop from '../components/Desktop';

const Index = () => {
  const [isBooting, setIsBooting] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsBooting(false);
    }, 2000); // 2 second boot sequence

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="h-screen w-screen overflow-hidden">
      {isBooting ? (
        <BootScreen onBootComplete={() => setIsBooting(false)} />
      ) : (
        <Desktop />
      )}
    </div>
  );
};

export default Index;
