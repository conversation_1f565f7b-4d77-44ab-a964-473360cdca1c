
import { useState } from 'react';

export type ShutdownType = 'shutdown' | 'logoff';

export const useShutdown = () => {
  const [shutdownType, setShutdownType] = useState<ShutdownType | null>(null);

  const triggerShutdown = (type: ShutdownType) => {
    setShutdownType(type);
  };

  const cancelShutdown = () => {
    setShutdownType(null);
  };

  const resetToDesktop = () => {
    setShutdownType(null);
  };

  return {
    isShuttingDown: !!shutdownType,
    shutdownType,
    triggerShutdown,
    cancelShutdown,
    resetToDesktop
  };
};
