
import RightMenuItem from './RightMenuItem';
import { FileText, ImageIcon, Music, Computer, Settings, HelpCircle, Search as SearchIcon } from 'lucide-react';

interface StartMenuRightColumnProps {
    handleOpenWindow: (id: string, name: string) => void;
}

const StartMenuRightColumn = ({ handleOpenWindow }: StartMenuRightColumnProps) => (
    <div className="w-1/2 p-2 space-y-0.5" style={{ backgroundColor: '#D4E1F4' }}>
        <RightMenuItem
            icon={<FileText size={20} className="text-blue-700"/>}
            name="My Documents"
            onClick={() => handleOpenWindow('my_documents', 'My Documents')}
        />
        <RightMenuItem
            icon={<ImageIcon size={20} className="text-blue-700"/>}
            name="My Pictures"
            onClick={() => handleOpenWindow('my_pictures', 'My Pictures')}
        />
        <RightMenuItem
            icon={<Music size={20} className="text-blue-700"/>}
            name="My Music"
            onClick={() => handleOpenWindow('my_music', 'Windows Media Player')}
        />
        <RightMenuItem
            icon={<Computer size={20} className="text-blue-700"/>}
            name="My Computer"
            onClick={() => handleOpenWindow('my_computer', 'My Computer')}
        />
        <div className="border-t border-[#A5B5D3] my-1 !mt-2 !mb-2"></div>
        <RightMenuItem
            icon={<Settings size={20} className="text-blue-700"/>}
            name="Control Panel"
            onClick={() => handleOpenWindow('control_panel', 'Control Panel')}
        />
        <div className="border-t border-[#A5B5D3] my-1 !mt-2 !mb-2"></div>
        <RightMenuItem
            icon={<HelpCircle size={20} className="text-blue-700"/>}
            name="Help and Support"
            onClick={() => handleOpenWindow('contact', 'Contact Info')}
        />
        <RightMenuItem icon={<SearchIcon size={20} className="text-blue-700"/>} name="Search" />
    </div>
);

export default StartMenuRightColumn;
