
import { useState } from 'react';

interface DesktopIconProps {
  icon: {
    id: string;
    name: string;
    icon: string;
    type: string;
  };
  onDoubleClick: () => void;
}

const DesktopIcon = ({ icon, onDoubleClick }: DesktopIconProps) => {
  const [isSelected, setIsSelected] = useState(false);
  const [clickCount, setClickCount] = useState(0);

  const handleClick = () => {
    setIsSelected(true);
    setClickCount(prev => prev + 1);

    // Reset selection after a short delay if no second click
    setTimeout(() => {
      if (clickCount === 0) {
        setIsSelected(false);
      }
    }, 300);

    // Handle double click
    if (clickCount === 1) {
      onDoubleClick();
      setClickCount(0);
      setIsSelected(false);
    }

    // Reset click count
    setTimeout(() => {
      setClickCount(0);
    }, 300);
  };

  return (
    <div
      className={`flex flex-col items-center w-20 p-2 cursor-pointer transition-all ${
        isSelected ? 'bg-blue-500 bg-opacity-30' : ''
      }`}
      style={{ outline: 'none', border: 'none' }}
      onClick={handleClick}
    >
      <div 
        className="h-10 mb-1 flex items-center justify-center"
      >
        <span className="text-3xl drop-shadow-lg">{icon.icon}</span>
      </div>
      <div className="text-white text-xs text-center leading-tight font-medium drop-shadow-lg">
        {icon.name}
      </div>
    </div>
  );
};

export default DesktopIcon;
