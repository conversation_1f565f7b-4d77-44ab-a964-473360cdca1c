
import { 
  SiDotnet, SiNodedotjs, SiMysql, SiPostgresql, SiSqlite, SiElasticsearch,
  SiDevexpress, SiMetabase, SiPostman, SiDocker, SiHtml5, SiCss3, 
  SiJavascript, SiAngular, SiReact, SiTypescript, SiJson, SiXml,
  SiGit, SiSubversion, SiGitlab, SiAsana, SiBitbucket, SiJira, 
  SiNotion, SiConfluence
} from 'react-icons/si';
import { 
  FaDatabase, FaTrello 
} from 'react-icons/fa';
import { 
  VscJson, VscVscode 
} from 'react-icons/vsc';
import { 
  DiVisualstudio, DiJqueryLogo, DiGithubBadge 
} from 'react-icons/di';

export const iconMap: { [key: string]: { icon: React.ElementType; color: string } } = {
  // Languages
  'C#': { icon: SiDotnet, color: 'text-purple-600' },
  'VB.NET': { icon: SiDotnet, color: 'text-blue-600' },
  'ASP.NET': { icon: SiDotnet, color: 'text-blue-500' },
  'Razor': { icon: SiDotnet, color: 'text-blue-500' },
  'Node.js': { icon: SiNodedotjs, color: 'text-green-600' },
  'T-SQL': { icon: FaDatabase, color: 'text-blue-700' },
  'LINQ': { icon: FaDatabase, color: 'text-purple-500' },
  'Web API': { icon: VscJson, color: 'text-yellow-600' },

  // Databases
  'MSSQL': { icon: FaDatabase, color: 'text-red-600' },
  'MySQL': { icon: SiMysql, color: 'text-orange-500' },
  'PostgreSQL': { icon: SiPostgresql, color: 'text-blue-600' },
  'SQLite': { icon: SiSqlite, color: 'text-blue-500' },
  'CosmosDB': { icon: FaDatabase, color: 'text-purple-600' },
  'NoSQL (ElasticSearch)': { icon: SiElasticsearch, color: 'text-yellow-500' },

  // Tools
  'Visual Studio': { icon: DiVisualstudio, color: 'text-purple-600' },
  'VS Code': { icon: VscVscode, color: 'text-blue-500' },
  'DevExpress': { icon: SiDevexpress, color: 'text-orange-500' },
  'Metabase': { icon: SiMetabase, color: 'text-blue-600' },
  'Postman': { icon: SiPostman, color: 'text-orange-500' },
  'Docker': { icon: SiDocker, color: 'text-blue-500' },

  // Technologies
  '.NET Framework & Core': { icon: SiDotnet, color: 'text-purple-600' },
  'ADO.NET': { icon: FaDatabase, color: 'text-blue-600' },
  'Dapper': { icon: FaDatabase, color: 'text-green-600' },
  'HTML': { icon: SiHtml5, color: 'text-orange-500' },
  'CSS': { icon: SiCss3, color: 'text-blue-500' },
  'JavaScript': { icon: SiJavascript, color: 'text-yellow-500' },
  'jQuery': { icon: DiJqueryLogo, color: 'text-blue-600' },
  'Angular': { icon: SiAngular, color: 'text-red-600' },
  'React': { icon: SiReact, color: 'text-cyan-500' },
  'TypeScript': { icon: SiTypescript, color: 'text-blue-600' },
  'JSON': { icon: SiJson, color: 'text-yellow-600' },
  'XML': { icon: SiXml, color: 'text-orange-600' },
  
  // Version Control
  'TFS': { icon: SiGit, color: 'text-blue-600' },
  'SVN': { icon: SiSubversion, color: 'text-blue-500' },
  'GitHub': { icon: DiGithubBadge, color: 'text-gray-800' },
  'GitLab': { icon: SiGitlab, color: 'text-orange-500' },
  
  // Project Management
  'Asana': { icon: SiAsana, color: 'text-red-500' },
  'Bitbucket': { icon: SiBitbucket, color: 'text-blue-600' },
  'JIRA': { icon: SiJira, color: 'text-blue-500' },
  'Notion': { icon: SiNotion, color: 'text-gray-800' },
  'Trello': { icon: FaTrello, color: 'text-blue-500' },
  'Confluence': { icon: SiConfluence, color: 'text-blue-600' },
};
