<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zendo - Browser Extension</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .project-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .features {
            list-style: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .features li:before {
            content: "✓ ";
            color: #4ade80;
            font-weight: bold;
        }
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .chrome-store-btn {
            display: inline-block;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);
            transition: all 0.3s ease;
        }
        .chrome-store-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
            background: linear-gradient(135deg, #3367d6 0%, #2d8f47 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧘 Zendo</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Minimalist to-do list with task grouping, drag-and-drop, and color priorities.
        </p>

        <!-- Screenshot Section -->
        <div style="text-align: center; margin-bottom: 30px;">
            <img src="z-task.png" alt="Zendo Extension Screenshot" style="max-width: 100%; height: auto; border-radius: 10px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);">
        </div>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">JavaScript</span>
                    <span class="tech-tag">Chrome Extension API</span>
                    <span class="tech-tag">HTML5</span>
                    <span class="tech-tag">CSS3</span>
                    <span class="tech-tag">Chrome Sync</span>
                </div>
            </div>

            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Published</p>
                <p><strong>Version:</strong> Latest</p>
                <p><strong>Platform:</strong> Chrome Web Store</p>
                <p><strong>Offline:</strong> Fully supported</p>
            </div>
        </div>

        <!-- Chrome Web Store Link -->
        <div style="text-align: center; margin-bottom: 30px;">
            <a href="https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo"
               target="_blank"
               class="chrome-store-btn">
                🚀 Install from Chrome Web Store
            </a>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>Grouped Tasks – Organize tasks by project, context, or theme</li>
                <li>Drag & Drop – Easily reorder tasks and groups with intuitive gestures</li>
                <li>Custom Priorities – Choose and personalize priority levels using color tags</li>
                <li>Zen-Inspired Design – Clean, minimal UI with Apple-style visual polish</li>
                <li>One-Click Complete & Edit – Click to check off tasks or edit inline</li>
                <li>Chrome Sync – Store tasks and settings across devices</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                <strong>Zendo – Grouped To-Do List & Task Manager for Chrome</strong><br>
                Zendo is a minimalist to-do list extension for Chrome that helps you organize tasks into groups,
                set color-coded priorities, and stay focused with a clean, drag-and-drop interface.
            </p>
            <p>
                <strong>Zendo is ideal for:</strong><br>
                • Daily task planning<br>
                • Project-based to-do management<br>
                • Students, creatives, and professionals seeking simplicity<br>
                • Anyone who prefers grouped over nested tasks
            </p>
            <p>
                Works fully offline and uses Chrome Sync to store your tasks and settings across devices.<br>
                <strong>No login. No tracking. No distractions.</strong>
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
