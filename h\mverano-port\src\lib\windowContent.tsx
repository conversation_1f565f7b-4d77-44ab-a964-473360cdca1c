import React from 'react';
import RecycleBinContent from '../components/windows/RecycleBinContent';
import SkillsContent from '../components/windows/SkillsContent';
import MyComputerContent from '../components/windows/MyComputerContent';
import MyDocumentsContent from '../components/windows/MyDocumentsContent';
import MyPicturesContent from '../components/windows/MyPicturesContent';
import MyMusicContent from '../components/windows/MyMusicContent';
import ControlPanelContent from '../components/windows/ControlPanelContent';
import ResumeContent from '../components/windows/ResumeContent';
import ContactContent from '../components/windows/ContactContent';

type OpenWindowFunc = (id: string, name: string) => void;

export const getWindowContent = (iconId: string, onOpenWindow: OpenWindowFunc): React.ReactNode => {
  switch (iconId) {
    case 'recyclebin':
      return <RecycleBinContent />;
    case 'my_computer':
      return <MyComputerContent />;
    case 'skills':
      return <SkillsContent />;
    case 'my_documents':
      return <MyDocumentsContent onOpenWindow={onOpenWindow} />;
    case 'my_pictures':
      return <MyPicturesContent />;
    case 'my_music':
      return <MyMusicContent />;
    case 'control_panel':
      return <ControlPanelContent />;
    case 'certifications':
      return (
        <div className="p-4">
          <h2 className="text-lg font-bold mb-4">Certifications</h2>
          <p>This is a placeholder for certifications. I would list my professional certifications here, if I had any.</p>
          <ul className="list-disc list-inside mt-2">
            <li>Example: Awesome Developer Certificate</li>
            <li>Example: Certified Tech Guru</li>
          </ul>
        </div>
      );
    case 'projects':
      return (
        <div className="p-4">
          <h2 className="text-lg font-bold mb-4">My Projects</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="border p-3 rounded">
              <h3 className="font-semibold">E-Commerce Platform</h3>
              <p className="text-sm text-gray-600">React, Node.js, PostgreSQL</p>
              <p className="text-xs mt-2">Full-stack web application with user authentication, payment processing, and admin dashboard.</p>
            </div>
            <div className="border p-3 rounded">
              <h3 className="font-semibold">Task Management App</h3>
              <p className="text-sm text-gray-600">React, TypeScript, Firebase</p>
              <p className="text-xs mt-2">Real-time collaborative task management with drag-and-drop functionality.</p>
            </div>
            <div className="border p-3 rounded">
              <h3 className="font-semibold">Weather Dashboard</h3>
              <p className="text-sm text-gray-600">Vue.js, Chart.js, REST API</p>
              <p className="text-xs mt-2">Interactive weather dashboard with historical data visualization.</p>
            </div>
            <div className="border p-3 rounded">
              <h3 className="font-semibold">Portfolio Website</h3>
              <p className="text-sm text-gray-600">React, Tailwind CSS</p>
              <p className="text-xs mt-2">Responsive portfolio showcasing projects and skills with modern design.</p>
            </div>
          </div>
        </div>
      );
    case 'my_resume':
      return <ResumeContent />;
    case 'contact':
      return <ContactContent />;
    case 'about':
      return (
        <div className="p-4 bg-white font-mono text-sm">
          <div className="border-b pb-2 mb-4">
            <span className="font-bold">About Me.txt</span>
          </div>
          <div className="whitespace-pre-line">
            {`Hello! I'm John Smith, a passionate software engineer with over 5 years of experience building web applications and scalable systems.

What I Do:
- Full-stack web development
- Cloud architecture and DevOps
- Mobile app development
- System design and optimization

My Approach:
I believe in writing clean, maintainable code and creating solutions that truly serve users' needs. I'm always excited to learn new technologies and tackle challenging problems.

Current Focus:
- Microservices architecture
- AI/ML integration
- Performance optimization
- Team leadership and mentoring

When I'm not coding, you can find me:
- Contributing to open source projects
- Reading about emerging technologies
- Playing video games (yes, including retro ones!)
- Hiking and photography

Thanks for visiting my Windows XP themed portfolio! This nostalgic interface showcases my skills while paying homage to the golden age of computing. 

Feel free to explore my projects and get in touch if you'd like to collaborate!

---
Last modified: 2024
File size: 1.2 KB`}
          </div>
        </div>
      );
    default:
      return <div className="p-4">Window content not found.</div>;
  }
};
