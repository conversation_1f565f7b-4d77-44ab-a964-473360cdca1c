
import { Folder, FileText } from 'lucide-react';

interface MyDocumentsContentProps {
  onOpenWindow: (id: string, name: string) => void;
}

const documents = [
  { id: 'projects', name: 'My Projects', icon: Folder, type: 'folder' },
  { id: 'my_resume', name: 'my_resume.pdf', icon: FileText, type: 'file' },
  { id: 'skills', name: 'Skills & Experience', icon: Folder, type: 'folder' },
  { id: 'certifications', name: 'Certifications', icon: Folder, type: 'folder' },
];

const MyDocumentsContent = ({ onOpenWindow }: MyDocumentsContentProps) => {
  
  const handleDoubleClick = (e: React.MouseEvent, id: string, name: string) => {
    e.stopPropagation();
    onOpenWindow(id, name);
  };

  return (
    <div className="p-4 bg-white font-tahoma h-full overflow-y-auto">
      <div className="flex items-center mb-4 pb-2 border-b-2 border-blue-500">
        <Folder size={32} className="text-yellow-600 mr-3" />
        <h2 className="text-xl font-bold text-blue-800">My Documents</h2>
      </div>
      <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {documents.map((doc) => {
          const Icon = doc.icon;
          return (
            <div
              key={doc.id}
              className="flex flex-col items-center justify-center text-center cursor-pointer p-2 rounded hover:bg-blue-100"
              onDoubleClick={(e) => handleDoubleClick(e, doc.id, doc.name)}
            >
              <Icon size={48} className={doc.id === 'my_resume' ? 'text-red-600' : doc.type === 'folder' ? 'text-yellow-500' : 'text-gray-700'} />
              <span className="mt-2 text-xs break-words w-full">{doc.name}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MyDocumentsContent;
